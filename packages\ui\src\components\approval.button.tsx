"use client";

import * as React from "react";
import { Button } from "@workspace/ui/components/button";
import {
  ApprovalPopover,
  type ApprovalPopoverProps,
} from "@workspace/ui/components/approval.popover";

export type ApprovalButtonProps = Omit<ApprovalPopoverProps, "children"> &
  Omit<React.ComponentProps<typeof Button>, "pending" | "processing"> & {
    children?: React.ReactNode;
    onOpen?: () => void;
    needApproval?: boolean;
  };

export function ApprovalButton({
  children,
  // ApprovalPopover props
  disclaimer,
  approveText,
  cancelText,
  onApprove,
  onCancel,
  open,
  defaultOpen,
  onOpenChange,
  align,
  side,
  sideOffset,
  contentClassName,
  // Button props
  disabled,
  onOpen,
  needApproval = true,
  ...buttonProps
}: ApprovalButtonProps) {
  const isControlled = open !== undefined;
  const [internalOpen, setInternalOpen] = React.useState<boolean>(
    defaultOpen ?? false,
  );
  const actualOpen = isControlled ? (open as boolean) : internalOpen;

  // Internal visual states for the trigger button
  const [pending, setPending] = React.useState(false);
  const [processing, setProcessing] = React.useState(false);

  // When popover opens, show pending visuals; when it closes, clear pending
  const handleOpenChange = React.useCallback(
    (next: boolean) => {
      if (!isControlled) setInternalOpen(next);
      setPending(next);
      // Do not touch `processing` here so that, after approval, the trigger
      // can remain in processing state while the async onApprove completes.

      // Call onOpen callback when dialog opens
      if (next) {
        onOpen?.();
      }

      onOpenChange?.(next);
    },
    [isControlled, onOpenChange, onOpen],
  );

  // Wrap onApprove to manage processing visuals while promise resolves
  const handleApprove = React.useCallback(() => {
    // Move from pending to processing
    setPending(false);
    setProcessing(true);
    const result = onApprove?.();
    if (result && typeof (result as any).then === "function") {
      (result as Promise<void>).finally(() => setProcessing(false));
    } else {
      setProcessing(false);
    }
  }, [onApprove]);

  const handleCancel = React.useCallback(() => {
    // Clear pending if user cancels; processing should already be false here
    setPending(false);
    onCancel?.();
  }, [onCancel]);

  // Handle direct approval when needApproval is false
  const handleButtonClick = React.useCallback(() => {
    if (!needApproval) {
      setProcessing(true);
      const result = onApprove?.();
      if (result && typeof (result as any).then === "function") {
        (result as Promise<void>).finally(() => setProcessing(false));
      } else {
        setProcessing(false);
      }
    }
  }, [needApproval, onApprove]);

  if (!needApproval) {
    return (
      <Button
        disabled={disabled || processing}
        processing={processing}
        onClick={handleButtonClick}
        {...buttonProps}
      >
        {children}
      </Button>
    );
  }

  return (
    <ApprovalPopover
      disclaimer={disclaimer}
      approveText={approveText}
      cancelText={cancelText}
      onApprove={handleApprove}
      onCancel={handleCancel}
      open={actualOpen}
      defaultOpen={defaultOpen}
      onOpenChange={handleOpenChange}
      align={align}
      side={side}
      sideOffset={sideOffset}
      contentClassName={contentClassName}
    >
      <Button
        disabled={disabled || processing || pending}
        pending={pending}
        processing={processing}
        {...buttonProps}
      >
        {children}
      </Button>
    </ApprovalPopover>
  );
}

export default ApprovalButton;

import * as React from "react";

interface BouncingCirclesProps extends React.SVGProps<SVGSVGElement> {
  size?: number | string;
}

const BouncingCircles = React.forwardRef<SVGSVGElement, BouncingCirclesProps>(
  ({ size = 24, className, ...props }, ref) => (
    <svg
      ref={ref}
      width={size}
      height={size}
      viewBox="0 0 200 200"
      fill="currentColor"
      stroke="currentColor"
      strokeWidth="0"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <circle r={15} cx={40} cy={65}>
        <animate
          attributeName="cy"
          calcMode="spline"
          dur="1.5"
          values="65;135;65;"
          keySplines=".5 0 .5 1;.5 0 .5 1"
          repeatCount="indefinite"
          begin="-.4"
        />
      </circle>
      <circle r={15} cx={100} cy={65}>
        <animate
          attributeName="cy"
          calcMode="spline"
          dur="1.5"
          values="65;135;65;"
          keySplines=".5 0 .5 1;.5 0 .5 1"
          repeatCount="indefinite"
          begin="-.2"
        />
      </circle>
      <circle r={15} cx={160} cy={65}>
        <animate
          attributeName="cy"
          calcMode="spline"
          dur="1.5"
          values="65;135;65;"
          keySplines=".5 0 .5 1;.5 0 .5 1"
          repeatCount="indefinite"
          begin="0"
        />
      </circle>
    </svg>
  ),
);

BouncingCircles.displayName = "BouncingCircles";

export default BouncingCircles;

import * as React from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";

interface TooltipNotificationProps {
  children: React.ReactNode;
  message: string;
  open?: boolean;
  tooltipContentProps?: React.ComponentProps<typeof TooltipContent>;
}

export function TooltipNotification({
  children,
  message,
  open,
  tooltipContentProps,
}: TooltipNotificationProps) {
  return (
    <Tooltip open={open}>
      <TooltipTrigger className="cursor-help" asChild>
        {children}
      </TooltipTrigger>
      <TooltipContent
        className="bg-destructive rounded-xs text-center -skew-x-12 w-46"
        arrowClassName="fill-destructive bg-destructive"
        {...tooltipContentProps}
      >
        <span className="skew-x-12">{message}</span>
      </TooltipContent>
    </Tooltip>
  );
}

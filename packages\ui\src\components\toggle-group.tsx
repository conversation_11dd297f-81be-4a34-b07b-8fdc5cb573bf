"use client";

import * as React from "react";
import * as ToggleGroupPrimitive from "@radix-ui/react-toggle-group";

import { cn } from "@workspace/ui/lib/utils";
import {
  segmentedContainerClasses,
  segmentedItemBaseClasses,
} from "@workspace/ui/components/tabs";

const itemClasses =
  "data-[state=off]:bg-gradient-to-t data-[state=on]:bg-primary data-[state=on]:inset-shadow-sm data-[state=on]:inset-shadow-orange-800/50 data-[state=on]:[&>*]:translate-y-px";

function ToggleGroup({
  className,
  children,
  ...props
}: React.ComponentProps<typeof ToggleGroupPrimitive.Root>) {
  return (
    <ToggleGroupPrimitive.Root
      data-slot="toggle-group"
      className={cn(segmentedContainerClasses, className)}
      {...props}
    >
      {children}
    </ToggleGroupPrimitive.Root>
  );
}

function ToggleGroupItem({
  className,
  children,
  ...props
}: React.ComponentProps<typeof ToggleGroupPrimitive.Item>) {
  return (
    <ToggleGroupPrimitive.Item
      data-slot="toggle-group-item"
      className={cn(segmentedItemBaseClasses, itemClasses, className)}
      {...props}
    >
      <div>{children}</div>
    </ToggleGroupPrimitive.Item>
  );
}

export { ToggleGroup, ToggleGroupItem };

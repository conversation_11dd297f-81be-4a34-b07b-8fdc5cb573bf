import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ist,
  Ta<PERSON>Trigger,
  TabsContent,
} from "@workspace/ui/components/tabs";

export default function Page() {
  return (
    <Tabs defaultValue="katalog">
      <TabsList>
        <TabsTrigger className="w-32" value="katalog">
          {"KATALOG"}
        </TabsTrigger>
        <TabsTrigger value="galeri">{"GALERİ"}</TabsTrigger>
        <TabsTrigger value="degerlendirme">{"DEĞERLENDİRME"}</TabsTrigger>
        <TabsTrigger value="soru-cevap">{"SORU/CEVAP"}</TabsTrigger>
      </TabsList>
      <TabsContent value="katalog"></TabsContent>
      <TabsContent value="galeri"></TabsContent>
      <TabsContent value="degerlendirme"></TabsContent>
      <TabsContent value="soru-cevap"></TabsContent>
    </Tabs>
  );
}

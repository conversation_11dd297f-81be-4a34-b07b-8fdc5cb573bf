"use client";

import * as React from "react";
import {
  ToggleGroup,
  ToggleGroupItem,
} from "@workspace/ui/components/toggle-group";
import { Bold, Italic, Underline } from "lucide-react";

export default function Page() {
  const [align, setAlign] = React.useState<string | undefined>("left");
  const [formats, setFormats] = React.useState<string[]>(["bold"]);

  return (
    <div className="flex gap-6 flex-col">
      {/* Single selection: numeric +/- */}
      <ToggleGroup
        type="single"
        defaultValue="decrement"
        aria-label="Change value"
      >
        <ToggleGroupItem value="decrement" aria-label="Decrement">
          {"Açık"}
        </ToggleGroupItem>
        <ToggleGroupItem value="increment" aria-label="Increment">
          {"Kapalı"}
        </ToggleGroupItem>
      </ToggleGroup>

      {/* Single selection: alignment (controlled) */}
      <ToggleGroup
        type="single"
        value={align}
        onValueChange={setAlign}
        aria-label="Text alignment"
      >
        <ToggleGroupItem value="left">Left</ToggleGroupItem>
        <ToggleGroupItem value="center">Center</ToggleGroupItem>
        <ToggleGroupItem value="right">Right</ToggleGroupItem>
      </ToggleGroup>

      {/* Disabled example within group */}
      <ToggleGroup type="single" defaultValue="tl" aria-label="Currency">
        <ToggleGroupItem className="w-21" value="tl">
          TL
        </ToggleGroupItem>
        <ToggleGroupItem value="usd" disabled>
          USD
        </ToggleGroupItem>
        <ToggleGroupItem value="eur">EUR</ToggleGroupItem>
      </ToggleGroup>

      {/* Multiple selection: text formatting (controlled) */}
      <ToggleGroup
        type="multiple"
        value={formats}
        onValueChange={setFormats}
        aria-label="Text formatting"
      >
        <ToggleGroupItem value="bold" aria-label="Bold">
          <Bold />
        </ToggleGroupItem>
        <ToggleGroupItem value="italic" aria-label="Italic">
          <Italic />
        </ToggleGroupItem>
        <ToggleGroupItem value="underline" aria-label="Underline">
          <Underline />
        </ToggleGroupItem>
      </ToggleGroup>

      {/* Multiple selection: ranges (uncontrolled) */}
      <ToggleGroup
        type="multiple"
        defaultValue={["1D"]}
        aria-label="Date ranges"
      >
        <ToggleGroupItem value="1D">1D</ToggleGroupItem>
        <ToggleGroupItem value="1W">1W</ToggleGroupItem>
        <ToggleGroupItem value="1M">1M</ToggleGroupItem>
        <ToggleGroupItem value="3M">3M</ToggleGroupItem>
        <ToggleGroupItem value="1Y">1Y</ToggleGroupItem>
      </ToggleGroup>
    </div>
  );
}

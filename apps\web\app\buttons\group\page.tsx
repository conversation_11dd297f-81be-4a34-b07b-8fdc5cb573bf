"use client";

import {
  ButtonGroup,
  ButtonGroupItem,
} from "@workspace/ui/components/button-group";
import { Minus, Plus } from "lucide-react";

export default function Page() {
  return (
    <div className="flex gap-4 flex-col">
      <ButtonGroup>
        <ButtonGroupItem>
          <Minus />
        </ButtonGroupItem>
        <ButtonGroupItem>
          <Plus />
        </ButtonGroupItem>
      </ButtonGroup>

      <ButtonGroup>
        <ButtonGroupItem className="w-21">{"TL"}</ButtonGroupItem>
        <ButtonGroupItem disabled>{"USD"}</ButtonGroupItem>
        <ButtonGroupItem>{"EUR"}</ButtonGroupItem>
      </ButtonGroup>

      <ButtonGroup>
        <ButtonGroupItem>{"1D"}</ButtonGroupItem>
        <ButtonGroupItem>{"1W"}</ButtonGroupItem>
        <ButtonGroupItem>{"1M"}</ButtonGroupItem>
        <ButtonGroupItem>{"3M"}</ButtonGroupItem>
        <ButtonGroupItem>{"1Y"}</ButtonGroupItem>
      </ButtonGroup>
    </div>
  );
}

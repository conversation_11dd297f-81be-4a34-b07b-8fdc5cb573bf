"use client";

import { Checkbox, CheckboxValue } from "@workspace/ui/components/checkbox";
import { Label } from "@workspace/ui/components/label";
import { useState } from "react";

export default function CheckboxShowcase() {
  const [checked, setChecked] = useState<CheckboxValue>(false);
  const [indeterminate, setIndeterminate] = useState<boolean | "indeterminate">(
    "indeterminate",
  );
  const [items, setItems] = useState([
    { id: "item1", label: "Item 1", checked: false },
    { id: "item2", label: "Item 2", checked: true },
    { id: "item3", label: "Item 3", checked: false },
  ]);

  const handleItemChange = (id: string, checked: boolean) => {
    setItems((prev) =>
      prev.map((item) => (item.id === id ? { ...item, checked } : item)),
    );
  };

  const allChecked = items.every((item) => item.checked);
  const someChecked = items.some((item) => item.checked);
  const selectAllState = allChecked
    ? true
    : someChecked
      ? "indeterminate"
      : false;

  const handleSelectAll = (checked: boolean | "indeterminate") => {
    const newChecked = checked === true;
    setItems((prev) => prev.map((item) => ({ ...item, checked: newChecked })));
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="space-y-2">
        <h1 className="text-2xl font-bold">Checkbox Component Showcase</h1>
        <p className="text-muted-foreground">
          Demonstrating various states and use cases of the Checkbox component.
        </p>
      </div>

      {/* Basic Usage */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Basic Usage</h2>
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox id="basic" />
            <Label htmlFor="basic">Basic checkbox</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="checked" defaultChecked />
            <Label htmlFor="checked">Checked by default</Label>
          </div>
        </div>
      </section>

      {/* Controlled State */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Controlled State</h2>
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="controlled"
              checked={checked}
              onCheckedChange={(checked) => setChecked(checked)}
            />
            <Label htmlFor="controlled">
              Controlled checkbox (currently:{" "}
              {checked ? "checked" : "unchecked"})
            </Label>
          </div>
          <button
            onClick={() => setChecked(!checked)}
            className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded"
          >
            Toggle from outside
          </button>
        </div>
      </section>

      {/* Indeterminate State */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Indeterminate State</h2>
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="indeterminate"
              checked={indeterminate}
              onCheckedChange={setIndeterminate}
            />
            <Label htmlFor="indeterminate">
              Indeterminate checkbox (state: {String(indeterminate)})
            </Label>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setIndeterminate(false)}
              className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded"
            >
              Unchecked
            </button>
            <button
              onClick={() => setIndeterminate("indeterminate")}
              className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded"
            >
              Indeterminate
            </button>
            <button
              onClick={() => setIndeterminate(true)}
              className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded"
            >
              Checked
            </button>
          </div>
        </div>
      </section>

      {/* Disabled State */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Disabled State</h2>
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox id="disabled-unchecked" disabled />
            <Label htmlFor="disabled-unchecked">Disabled unchecked</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="disabled-checked" disabled defaultChecked />
            <Label htmlFor="disabled-checked">Disabled checked</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="disabled-indeterminate"
              disabled
              checked="indeterminate"
            />
            <Label htmlFor="disabled-indeterminate">
              Disabled indeterminate
            </Label>
          </div>
        </div>
      </section>

      {/* Size Variants */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Size Variants</h2>
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox id="size-default" size="default" />
            <Label htmlFor="size-default">Default size</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="size-large" size="large" />
            <Label htmlFor="size-large">Large size</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="size-huge" size="huge" />
            <Label htmlFor="size-huge">Huge size</Label>
          </div>
        </div>
      </section>

      {/* Select All Pattern */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Select All Pattern</h2>
        <div className="space-y-3 p-4 border rounded-lg">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="select-all"
              checked={selectAllState}
              onCheckedChange={handleSelectAll}
            />
            <Label htmlFor="select-all" className="font-medium">
              Select All ({items.filter((item) => item.checked).length}/
              {items.length})
            </Label>
          </div>
          <div className="ml-6 space-y-2">
            {items.map((item) => (
              <div key={item.id} className="flex items-center space-x-2">
                <Checkbox
                  id={item.id}
                  checked={item.checked}
                  onCheckedChange={(checked) =>
                    handleItemChange(item.id, checked as boolean)
                  }
                />
                <Label htmlFor={item.id}>{item.label}</Label>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Form Integration */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Form Integration</h2>
        <form className="space-y-3 p-4 border rounded-lg">
          <div className="flex items-center space-x-2">
            <Checkbox id="terms" required />
            <Label htmlFor="terms">
              I agree to the{" "}
              <a href="#" className="text-primary underline">
                terms and conditions
              </a>
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="newsletter" />
            <Label htmlFor="newsletter">Subscribe to newsletter</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="marketing" />
            <Label htmlFor="marketing">Receive marketing emails</Label>
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-primary text-primary-foreground rounded"
          >
            Submit
          </button>
        </form>
      </section>

      {/* Accessibility */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Accessibility Features</h2>
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="aria-describedby"
              aria-describedby="checkbox-description"
            />
            <Label htmlFor="aria-describedby">Checkbox with description</Label>
          </div>
          <p
            id="checkbox-description"
            className="text-sm text-muted-foreground ml-6"
          >
            This checkbox has an associated description for screen readers.
          </p>

          <div className="flex items-center space-x-2">
            <Checkbox id="aria-invalid" aria-invalid />
            <Label htmlFor="aria-invalid">
              Invalid checkbox (with error styling)
            </Label>
          </div>
        </div>
      </section>
    </div>
  );
}

# Number Formatting Guidelines

## Turkish Locale Currency Formatting

### Format Requirements

For Turkish Lira (TL) amounts, use the following formatting:

- **Correct**: `100.000,00 TL`
- **Incorrect**: `100000.00 TL`

### Rules

1. **Decimal separator**: Use comma (`,`) before the last two digits
2. **Thousands separator**: Use period (`.`) for every 3 digits
3. **Decimal places**: Always show 2 decimal places

### Examples

| Amount | Correct Format | Incorrect Format |
|--------|----------------|------------------|
| 1000.50 | 1.000,50 TL | 1000.50 TL |
| 12500.75 | 12.500,75 TL | 12500.75 TL |
| 100000.00 | 100.000,00 TL | 100000.00 TL |
| 1250000.25 | 1.250.000,25 TL | 1250000.25 TL |

### Implementation

Use the `formatTurkishCurrency` function for all TL amounts:

```typescript
// Turkish locale number formatting function
const formatTurkishCurrency = (amount: number): string => {
  return new Intl.NumberFormat('tr-TR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Usage
const amount = 12500.75;
const formatted = formatTurkishCurrency(amount); // "12.500,75"
const display = `${formatted} TL`; // "12.500,75 TL"
```

### When NOT to Apply

- **SODA amounts**: Do not apply Turkish formatting to SODA values
- **Quantities**: Only apply to Turkish Lira currency amounts
- **Input fields**: May interfere with user input, apply only to display

### Components Using This Format

- Withdrawal Dialog: Brüt, Komisyon, Net amounts
- Feedback Dialog: All TL amounts in confirmation messages
- Any other currency display throughout the application

### Browser Support

The `Intl.NumberFormat` with 'tr-TR' locale is supported in all modern browsers.

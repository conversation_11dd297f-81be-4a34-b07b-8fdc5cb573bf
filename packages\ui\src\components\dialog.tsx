"use client";

import * as React from "react";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { XIcon } from "lucide-react";

import { cn } from "@workspace/ui/lib/utils";

// Global state for tracking the active dialog
let latestDialogTimestamp = 0;
const dialogListeners = new Set<(timestamp: number) => void>();

const setLatestDialog = (timestamp: number) => {
  latestDialogTimestamp = timestamp;
  // Make it accessible for debugging
  if (typeof window !== "undefined") {
    (window as any).latestDialogTimestamp = timestamp;
  }
  dialogListeners.forEach((listener) => listener(timestamp));
};

// Context for tracking nested dialogs
interface DialogContextValue {
  level: number;
  isNested: boolean;
  dialogTimestamp: number;
  latestTimestamp: number;
}

const DialogContext = React.createContext<DialogContextValue>({
  level: 0,
  isNested: false,
  dialogTimestamp: 0,
  latestTimestamp: 0,
});

const useDialogContext = () => React.useContext(DialogContext);

function Dialog({
  open,
  onOpenChange,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Root>) {
  const parentContext = useDialogContext();
  const currentLevel = parentContext.level + 1;
  const [currentLatestTimestamp, setCurrentLatestTimestamp] = React.useState(
    latestDialogTimestamp,
  );

  // Generate unique timestamp for this dialog
  const dialogTimestamp = React.useMemo(() => {
    return Date.now() + Math.random(); // Add random to ensure uniqueness
  }, []);

  // Subscribe to latest dialog changes
  React.useEffect(() => {
    const listener = (timestamp: number) =>
      setCurrentLatestTimestamp(timestamp);
    dialogListeners.add(listener);
    return () => {
      dialogListeners.delete(listener);
    };
  }, []);

  const contextValue: DialogContextValue = {
    level: currentLevel,
    isNested: currentLevel > 1,
    dialogTimestamp,
    latestTimestamp: currentLatestTimestamp,
  };

  return (
    <DialogContext.Provider value={contextValue}>
      <DialogPrimitive.Root
        data-slot="dialog"
        open={open}
        onOpenChange={onOpenChange}
        {...props}
      />
    </DialogContext.Provider>
  );
}

function DialogTrigger({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {
  return <DialogPrimitive.Trigger data-slot="dialog-trigger" {...props} />;
}

function DialogPortal({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Portal>) {
  return <DialogPrimitive.Portal data-slot="dialog-portal" {...props} />;
}

function DialogClose({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Close>) {
  return <DialogPrimitive.Close data-slot="dialog-close" {...props} />;
}

function DialogOverlay({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {
  const { level } = useDialogContext();

  // Always show the parent overlay (level 1), hide child overlays (level > 1)
  const shouldShow = level === 1;

  return (
    <DialogPrimitive.Overlay
      data-slot="dialog-overlay"
      className={cn(
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",
        // Hide child dialog overlays, keep parent overlay always visible
        !shouldShow && "opacity-0",
        className,
      )}
      {...props}
    />
  );
}

function DialogContent({
  className,
  children,
  showCloseButton = true,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Content> & {
  showCloseButton?: boolean;
}) {
  const { dialogTimestamp, latestTimestamp } = useDialogContext();

  // When this content mounts, set this dialog as the latest
  React.useEffect(() => {
    // Use a small delay to ensure this is the latest dialog to mount
    const timeoutId = setTimeout(() => {
      setLatestDialog(dialogTimestamp);
    }, 0);

    return () => {
      clearTimeout(timeoutId);
      // When unmounting, reset to 0 (no active dialog)
      setLatestDialog(0);
    };
  }, [dialogTimestamp]);

  // Check if there are multiple dialog contents
  const [totalDialogContents, setTotalDialogContents] = React.useState(1);

  React.useEffect(() => {
    const updateCount = () => {
      const count = document.querySelectorAll(
        '[data-slot="dialog-content"]',
      ).length;
      setTotalDialogContents(count);
    };

    // Update count immediately and on a timer
    updateCount();
    const interval = setInterval(updateCount, 100);

    return () => clearInterval(interval);
  }, []);

  // Only show content for the latest dialog, but if there's only one dialog, always show it
  const isLatest = dialogTimestamp === latestTimestamp;
  const shouldShow = totalDialogContents === 1 || isLatest;

  return (
    <DialogPortal data-slot="dialog-portal">
      <DialogOverlay />
      <DialogPrimitive.Content
        data-slot="dialog-content"
        className={cn(
          "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border-2 p-3 shadow-lg duration-200 sm:max-w-lg select-none max-h-screen overflow-y-auto overflow-x-hidden sm:overflow-visible scrollbar outline-0",
          // Set opacity to 0 for non-active dialog content
          !shouldShow && "opacity-0",
          className,
        )}
        {...props}
      >
        {showCloseButton && (
          <DialogPrimitive.Close
            data-slot="dialog-close"
            className="ring-offset-background w-min mt-2 -mb-4 ml-auto mr-2 sm:m-0 sm:absolute sm:-top-4 sm:-right-4 sm:translate-0 rounded-xs text-background border-2 inset-ring-ring inset-ring-2 drop-shadow drop-shadow-border/25 rotate-45 bg-foreground cursor-pointer z-1000"
          >
            <XIcon className="size-9 sm:size-10 p-1 -rotate-45" />
            <span className="sr-only">{"Kapat"}</span>
          </DialogPrimitive.Close>
        )}
        {children}
      </DialogPrimitive.Content>
    </DialogPortal>
  );
}

function DialogHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="dialog-header"
      className={cn("flex flex-col gap-2 text-center sm:text-left ", className)}
      {...props}
    />
  );
}

function DialogFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="dialog-footer"
      className={cn(
        "flex gap-4 flex-row justify-center mt-4 pb-2 mb-2 border-b-6 border-foreground",
        className,
      )}
      {...props}
    />
  );
}

function DialogTitle({
  className,
  variant = "default",
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Title> & {
  variant?: "default" | "stripe";
}) {
  return variant === "default" ? (
    <DialogPrimitive.Title
      data-slot="dialog-title"
      className={cn(
        "relative pt-1 text-center text-lg leading-none font-semibold after:content-[''] after:border-t-6 after:border-b-2 after:border-foreground after:block after:w-full after:pt-1 after:mt-1.5",
        className,
      )}
      {...props}
    />
  ) : (
    <div className="shrink-0 flex grow relative overflow-hidden gap-1.5 h-fit -left-3">
      <DialogPrimitive.Title
        data-slot="dialog-title"
        className={cn(
          "relative overflow-hidden text-xl text-shadow-none text-background pt-1.5 pb-1 pl-6 pr-12 before:content-[''] before:bg-foreground before:inset-0 before:absolute before:-z-10 before:-translate-x-4 before:-skew-x-14",
          className,
        )}
        {...props}
      />
      <div className="-skew-x-14 flex gap-1.5 -translate-x-4">
        <div className="w-2 bg-foreground"></div>
        <div className="w-2 bg-foreground"></div>
      </div>
    </div>
  );
}

function DialogDescription({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Description>) {
  return (
    <DialogPrimitive.Description
      data-slot="dialog-description"
      className={cn("text-muted-foreground text-sm px-8 pt-6 pb-4", className)}
      {...props}
    />
  );
}

export {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
};

import { But<PERSON> } from "@workspace/ui/components/button";
import { Toggle } from "@workspace/ui/components/toggle";
import { Search } from "@workspace/ui/components/search";
import { Heart, Plus, Settings } from "lucide-react";

export default function Page() {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">Button Sizes Test</h1>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Default Size</h2>
        <div className="flex gap-4 items-center">
          <Button>Default Button</Button>
          <Button variant="primary">Primary Button</Button>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Icon Size (Fixed 40x40px)</h2>
        <div className="flex gap-4 items-center">
          <Button size="icon">
            <Heart />
          </Button>
          <Button size="icon" variant="primary">
            <Plus />
          </Button>
          <Button size="icon" variant="secondary">
            <Settings />
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">
          Small Size (Grows horizontally)
        </h2>
        <div className="flex gap-4 items-center">
          <Button size="small">Save</Button>
          <Button size="small" variant="primary">
            Save Changes
          </Button>
          <Button size="small" variant="secondary">
            Cancel Operation
          </Button>
          <Button size="small">Very Long Button Text That Should Grow</Button>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Toggle Sizes</h2>
        <div className="flex gap-4 items-center">
          <Toggle size="icon">
            <Heart />
          </Toggle>
          <Toggle size="small">Toggle Text</Toggle>
          <Toggle>Default Toggle</Toggle>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Search Component</h2>
        <div className="flex gap-4 items-center">
          <Search placeholder="Search with icon size" size="icon" />
          <Search placeholder="Search with small size" size="small" />
          <Search placeholder="Search with default size" size="default" />
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Mixed Content</h2>
        <div className="flex gap-4 items-center">
          <Button size="icon">
            <Plus />
          </Button>
          <Button size="small">Add Item</Button>
          <Button>Create New Project</Button>
        </div>
      </div>
    </div>
  );
}

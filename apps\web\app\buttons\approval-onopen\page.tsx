"use client";

import * as React from "react";
import { ApprovalButton } from "@workspace/ui/components/approval.button";
import { InputValidated } from "@workspace/ui/components/input-validated";
import { Label } from "@workspace/ui/components/label";

export default function TestApprovalOnOpenPage() {
  const [isApprovalDialogOpen, setIsApprovalDialogOpen] = React.useState(false);
  const [formData, setFormData] = React.useState({
    name: "",
    email: "",
  });
  const [lastAction, setLastAction] = React.useState<string | null>(null);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="w-md mx-auto p-6 space-y-8">
      <section className="space-y-4">
        <h1 className="text-2xl font-bold">ApprovalButton onOpen Callback Test</h1>
        <p className="text-sm text-muted-foreground">
          This page demonstrates the new onOpen callback functionality. 
          When you click the approval button, the form inputs should become disabled 
          until the approval dialog is closed.
        </p>
        
        <div className="text-sm text-muted-foreground space-y-1">
          <div>Approval Dialog Open: {isApprovalDialogOpen ? "Yes" : "No"}</div>
          <div>Last Action: {lastAction ?? "-"}</div>
        </div>
      </section>

      <section className="space-y-4 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold">Test Form</h2>
        
        <div className="space-y-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Name</Label>
            <InputValidated
              id="name"
              name="name"
              disabled={isApprovalDialogOpen}
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Enter your name"
            />
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="email">Email</Label>
            <InputValidated
              id="email"
              name="email"
              type="email"
              disabled={isApprovalDialogOpen}
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              placeholder="Enter your email"
            />
          </div>
        </div>

        <div className="pt-4">
          <ApprovalButton
            disclaimer="Are you sure you want to submit this form? This action cannot be undone."
            approveText="Submit"
            cancelText="Cancel"
            variant="primary"
            onOpen={() => {
              setIsApprovalDialogOpen(true);
              setLastAction("Approval dialog opened - form inputs disabled");
            }}
            onOpenChange={(open) => {
              if (!open) {
                setIsApprovalDialogOpen(false);
                setLastAction("Approval dialog closed - form inputs enabled");
              }
            }}
            onApprove={async () => {
              // Simulate async work
              await new Promise((r) => setTimeout(r, 2000));
              setLastAction(`Form submitted with: ${formData.name}, ${formData.email}`);
            }}
            onCancel={() => {
              setLastAction("Form submission cancelled");
            }}
          >
            Submit Form
          </ApprovalButton>
        </div>
      </section>

      <section className="space-y-4">
        <h2 className="text-lg font-semibold">Instructions</h2>
        <ol className="list-decimal list-inside space-y-2 text-sm">
          <li>Fill in the form fields above</li>
          <li>Click the "Submit Form" button</li>
          <li>Notice that the form inputs become disabled when the approval dialog opens</li>
          <li>Try clicking "Cancel" - the inputs should become enabled again</li>
          <li>Try clicking "Submit Form" again and then "Submit" - the inputs should remain disabled during processing</li>
        </ol>
      </section>
    </div>
  );
}

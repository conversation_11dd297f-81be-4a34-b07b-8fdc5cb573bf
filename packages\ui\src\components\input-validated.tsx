"use client";

import * as React from "react";
import { TriangleAlert } from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import { Input } from "@workspace/ui/components/input";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";

export interface InputValidatedProps extends React.ComponentProps<"input"> {
  /** The validation message to display inside the tooltip */
  message?: string;
  /** Validation status; when provided together with message, renders the icon + tooltip */
  status?: "warning" | "error";
}

export const InputValidated = React.forwardRef<
  HTMLInputElement,
  InputValidatedProps
>(
  (
    { className, message, status, "aria-invalid": ariaInvalidProp, ...props },
    ref,
  ) => {
    const showValidation = Boolean(message && status);

    // Only set aria-invalid when in error state and the caller didn't already set it
    const ariaInvalid =
      ariaInvalidProp ?? (status === "error" ? true : undefined);

    return (
      <div className="relative w-full">
        <Input
          ref={ref}
          aria-invalid={ariaInvalid as any}
          className={cn(showValidation && "pr-9", className)}
          {...props}
        />
        {showValidation && (
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                type="button"
                aria-label={
                  typeof message === "string" ? message : "Validation message"
                }
                className={cn(
                  "cursor-auto absolute right-2 top-2 grid place-items-center",
                  status === "error" && "text-destructive",
                  status === "warning" && "text-warning",
                )}
                tabIndex={0}
              >
                <TriangleAlert className="size-6 -translate-y-px" />
              </button>
            </TooltipTrigger>
            <TooltipContent>{message}</TooltipContent>
          </Tooltip>
        )}
      </div>
    );
  },
);

InputValidated.displayName = "InputValidated";

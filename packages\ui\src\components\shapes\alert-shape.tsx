import * as React from "react";
import { cn } from "@workspace/ui/lib/utils";

interface AlertShapeProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  variant?: "destructive" | "warning" | "info";
}

export const AlertShape = React.forwardRef<HTMLDivElement, AlertShapeProps>(
  ({ children, className, variant = "destructive", ...props }, ref) => {
    const variantStyles = {
      destructive: "border-destructive bg-destructive",
      warning: "border-yellow-500 bg-yellow-500",
      info: "border-blue-500 bg-blue-500",
    };

    return (
      <div
        ref={ref}
        className={cn(
          "shrink-0 text-background border inset-ring-ring inset-ring-2 size-5 rotate-45 flex items-center justify-center drop-shadow-sm drop-shadow-border/25",
          variantStyles[variant],
          className,
        )}
        {...props}
      >
        <span className="text-shadow-none -rotate-45 text-base pl-px">
          {children}
        </span>
      </div>
    );
  },
);

AlertShape.displayName = "AlertShape";

"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useMemo, useState } from "react";

export function FolderNavigation() {
  const pathname = usePathname();
  const [folders, setFolders] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getFolders = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `/api/folders?route=${encodeURIComponent(pathname)}`,
        );
        if (response.ok) {
          const data = await response.json();
          setFolders(Array.isArray(data.folders) ? data.folders : []);
        } else {
          console.error("Failed to fetch folders");
          setFolders([]);
        }
      } catch (error) {
        console.error("Error fetching folders:", error);
        setFolders([]);
      } finally {
        setLoading(false);
      }
    };

    getFolders();
  }, [pathname]);

  // Client-side safety: filter out anything that looks like a file (has a dot)
  const visibleFolders = useMemo(
    () => folders.filter((name) => !!name && !name.includes(".")),
    [folders],
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-svh">
        <div className="text-foreground">Loading...</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-2">
      {visibleFolders.length === 0 ? (
        <div className="text-foreground">No folders found in current route</div>
      ) : (
        visibleFolders.map((folderName) => (
          <Link
            key={folderName}
            href={`${pathname === "/" ? "" : pathname}/${folderName}`}
            className="text-foreground hover:underline"
          >
            {folderName}
          </Link>
        ))
      )}
    </div>
  );
}

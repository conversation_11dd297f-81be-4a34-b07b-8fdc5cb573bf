"use client";

import { useState } from "react";
import { WithdrawalDialog } from "@/components/withdrawal.dialog";

export default function Page() {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <WithdrawalDialog
      open={isOpen}
      onOpenChange={setIsOpen}
      balance={12500}
      min={20}
      max={10000}
      onSubmit={(data) => {
        console.log("Withdrawal submitted:", data);
      }}
    />
  );
}

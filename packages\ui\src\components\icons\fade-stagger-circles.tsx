import * as React from "react";

interface FadeStaggerCirclesProps extends React.SVGProps<SVGSVGElement> {
  size?: number | string;
}

const FadeStaggerCircles = React.forwardRef<
  SVGSVGElement,
  FadeStaggerCirclesProps
>(({ size = 24, className, ...props }, ref) => (
  <svg
    ref={ref}
    width={size}
    height={size}
    viewBox="0 0 200 200"
    fill="currentColor"
    stroke="currentColor"
    strokeWidth="0"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <circle r={15} cx={40} cy={100}>
      <animate
        attributeName="opacity"
        calcMode="spline"
        dur="2"
        values="1;0;1;"
        keySplines=".5 0 .5 1;.5 0 .5 1"
        repeatCount="indefinite"
        begin="-.4"
      />
    </circle>
    <circle r={15} cx={100} cy={100}>
      <animate
        attributeName="opacity"
        calcMode="spline"
        dur="2"
        values="1;0;1;"
        keySplines=".5 0 .5 1;.5 0 .5 1"
        repeatCount="indefinite"
        begin="-.2"
      />
    </circle>
    <circle r={15} cx={160} cy={100}>
      <animate
        attributeName="opacity"
        calcMode="spline"
        dur="2"
        values="1;0;1;"
        keySplines=".5 0 .5 1;.5 0 .5 1"
        repeatCount="indefinite"
        begin="0"
      />
    </circle>
  </svg>
));

FadeStaggerCircles.displayName = "FadeStaggerCircles";

export default FadeStaggerCircles;

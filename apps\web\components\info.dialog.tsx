"use client";

import * as React from "react";
import { Button } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import { Separator } from "@workspace/ui/components/separator";

export interface InfoDialogProps {
  // Dialog state
  open?: boolean;
  onOpenChange?: (open: boolean) => void;

  // Content props
  title?: string;
  subtitle?: string;
  items?: string[];

  // Button text
  closeButtonText?: string;

  // Event handlers
  onClose?: () => void;

  // Trigger props
  trigger?: React.ReactNode;
}

export function InfoDialog({
  open,
  onOpenChange,
  title = "Bilgilendirme",
  subtitle,
  items = [],
  closeButtonText = "ANLADIM",
  onClose,
  trigger,
}: InfoDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent showCloseButton={false} className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {subtitle && (
            <DialogDescription className="text-foreground text-center">
              {subtitle}
            </DialogDescription>
          )}
        </DialogHeader>

        <main className="bg-muted rounded-lg mt-3.5 mx-5 pl-6 pr-3 pt-5 pb-9 space-y-4 overflow-y-auto max-h-[330px] text-muted-foreground text-sm leading-6 scrollbar">
          {items.map((item, index) => (
            <div key={index}>
              <div className="flex gap-2 items-start">
                <span className="mr-3 text-background bg-foreground -skew-x-16 mt-1.5 h-5 py-1 px-2.5 leading-0 text-shadow-none flex items-center rounded-xs">
                  <span className="skew-x-16">{index + 1}</span>
                </span>{" "}
                <span className="fake-text-stroke-muted"> {item}</span>
              </div>
              {index !== items.length - 1 && <Separator />}
            </div>
          ))}
        </main>

        <DialogFooter className="flex justify-center">
          <DialogClose asChild>
            <Button size={"small"} className="w-fit px-5" onClick={onClose}>
              {closeButtonText}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

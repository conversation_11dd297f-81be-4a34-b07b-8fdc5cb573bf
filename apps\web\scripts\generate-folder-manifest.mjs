import fs from "node:fs/promises";
import path from "node:path";

const ROOT = path.join(process.cwd(), "app");
const OUT_FILE = path.join(
  process.cwd(),
  "app",
  "api",
  "folders",
  "manifest.ts",
);

const EXCLUDED = new Set(["node_modules", "utils", "api", "components"]);

async function listSubfolders(dir) {
  const entries = await fs.readdir(dir, { withFileTypes: true });
  return entries
    .filter((e) => e.isDirectory())
    .map((e) => e.name)
    .filter(
      (name) =>
        !!name &&
        !name.startsWith(".") &&
        !name.includes(".") &&
        !name.startsWith("_") &&
        !EXCLUDED.has(name),
    )
    .sort((a, b) => a.localeCompare(b));
}

async function buildManifest() {
  const manifest = {};

  async function walk(dir, route) {
    const children = await listSubfolders(dir);
    manifest[route] = children;

    // Recurse into each child directory
    for (const name of children) {
      const nextDir = path.join(dir, name);
      const nextRoute = route === "/" ? `/${name}` : `${route}/${name}`;
      await walk(nextDir, nextRoute);
    }
  }

  await walk(ROOT, "/");

  const content = `export type FolderManifest = Record<string, string[]>;\n\nexport const folderManifest: FolderManifest = ${JSON.stringify(
    manifest,
    null,
    2,
  )} as const;\n`;

  await fs.writeFile(OUT_FILE, content, "utf-8");
  console.log(`Folder manifest written to ${OUT_FILE}`);
}

buildManifest().catch((err) => {
  console.error("Failed to generate folder manifest:", err);
  process.exit(1);
});

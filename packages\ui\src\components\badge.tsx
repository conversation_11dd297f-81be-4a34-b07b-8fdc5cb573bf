import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@workspace/ui/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-full pl-3 pr-5 py-1 text-xs text-shadow-none w-fit whitespace-nowrap shrink-0 [&>svg]:size-3.5 [&>svg]:stroke-3 gap-1.5  transition-[color,box-shadow] overflow-hidden border-2",
  {
    variants: {
      variant: {
        default: "border-foreground text-foreground",
        warning: "border-warning text-warning",
        destructive: "border-destructive text-destructive",
        success: "border-success text-success",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<"span"> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "span";

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant }), className)}
      {...props}
    />
  );
}

export { Badge, badgeVariants };

import { NextRequest, NextResponse } from "next/server";
import fs from "node:fs/promises";
import path from "node:path";
import { folderManifest } from "./manifest";

// Use Node.js runtime so local/dev can use fs; on Vercel we return the manifest
export const runtime = "nodejs";

const EXCLUDED = new Set(["node_modules", "utils", "api", "components"]);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const route = searchParams.get("route") || "";

    // Normalize route
    const normalized =
      route === "/" || route === "" ? "/" : route.replace(/\/$/, "");

    const isVercel =
      process.env.VERCEL === "1" || process.env.VERCEL === "true";

    if (isVercel) {
      // On Vercel, use the manifest (generated at build-time)
      const folders = folderManifest[normalized] ?? [];
      return NextResponse.json({ folders });
    }

    // Local/dev fallback: read from source app directory
    const appDir = path.join(process.cwd(), "app");
    const routeParts = normalized.split("/").filter(Boolean);
    const targetDir = path.join(appDir, ...routeParts);

    let entries;
    try {
      entries = await fs.readdir(targetDir, { withFileTypes: true });
    } catch {
      // If missing, fall back to app root
      entries = await fs.readdir(appDir, { withFileTypes: true });
    }

    const folders = entries
      .filter((e) => e.isDirectory())
      .map((e) => e.name)
      .filter(
        (name) =>
          !!name &&
          !name.startsWith(".") &&
          !name.includes(".") &&
          !name.startsWith("_") &&
          !EXCLUDED.has(name),
      )
      .sort((a, b) => a.localeCompare(b));

    return NextResponse.json({ folders });
  } catch (error) {
    console.error("Error in folders API:", error);
    return NextResponse.json(
      { error: "Failed to fetch folders" },
      { status: 500 },
    );
  }
}

"use client";

import * as React from "react";
import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { CheckIcon, MinusIcon } from "lucide-react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@workspace/ui/lib/utils";

// Shared checkbox variants using cva
const checkboxVariants = cva(
  "peer dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=indeterminate]:bg-primary data-[state=indeterminate]:text-primary-foreground data-[state=indeterminate]:border-primary aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shrink-0 rounded-[4px] transition-shadow disabled:cursor-not-allowed disabled:opacity-50 flex items-center justify-center text-current transition-none",
  {
    variants: {
      size: {
        default: "size-4 border border-input shadow-xs",
        large: "size-5 border-2 border-border",
        huge: "size-7 border-2 rounded-sm bg-background",
      },
    },
    defaultVariants: {
      size: "default",
    },
  },
);

// Shared checkbox icon variants
const checkboxIconVariants = cva("", {
  variants: {
    size: {
      default: "size-3.5",
      large: "stroke-3 size-3.5",
      huge: "size-5 stroke-4",
    },
  },
  defaultVariants: {
    size: "default",
  },
});

export type CheckboxValue = boolean | "indeterminate";

function Checkbox({
  className,
  size,
  checked,
  ...props
}: React.ComponentProps<typeof CheckboxPrimitive.Root> &
  VariantProps<typeof checkboxVariants> & {
    checked?: CheckboxValue;
  }) {
  return (
    <CheckboxPrimitive.Root
      data-slot="checkbox"
      className={cn(checkboxVariants({ size }), className)}
      checked={checked === "indeterminate" ? "indeterminate" : checked}
      {...props}
    >
      <CheckboxPrimitive.Indicator data-slot="checkbox-indicator">
        {checked === "indeterminate" ? (
          <MinusIcon className={checkboxIconVariants({ size })} />
        ) : (
          <CheckIcon className={checkboxIconVariants({ size })} />
        )}
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  );
}

export { Checkbox, checkboxVariants, checkboxIconVariants };

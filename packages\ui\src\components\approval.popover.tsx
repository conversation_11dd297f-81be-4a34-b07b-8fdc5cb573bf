"use client";

import * as React from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { Button } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";

export type ApprovalPopoverProps = {
  // The main trigger element (e.g., a primary action button)
  children: React.ReactNode;

  // Text/message shown inside the popover to warn the user
  disclaimer?: React.ReactNode;

  // Button labels
  approveText?: string;
  cancelText?: string;

  // Callbacks (may be async)
  onApprove?: () => void | Promise<void>;
  onCancel?: () => void | Promise<void>;

  // Controlled/uncontrolled state
  open?: boolean;
  defaultOpen?: boolean;
  onOpenChange?: (open: boolean) => void;

  // Positioning/customization forwarded to PopoverContent
  align?: React.ComponentProps<typeof PopoverContent>["align"];
  side?: React.ComponentProps<typeof PopoverContent>["side"];
  sideOffset?: number;
  contentClassName?: string;
};

export function ApprovalPopover({
  children,
  disclaimer = "Devam etmek istediğinize emin misiniz?",
  approveText = "Onayla",
  cancelText = "İptal",
  onApprove,
  onCancel,
  open,
  defaultOpen,
  onOpenChange,
  align = "center",
  side = "top",
  sideOffset,
  contentClassName,
}: ApprovalPopoverProps) {
  const isControlled = open !== undefined;
  const [internalOpen, setInternalOpen] = React.useState<boolean>(
    defaultOpen ?? false,
  );
  const actualOpen = isControlled ? (open as boolean) : internalOpen;

  const setOpen = React.useCallback(
    (next: boolean) => {
      if (!isControlled) setInternalOpen(next);
      onOpenChange?.(next);
    },
    [isControlled, onOpenChange],
  );

  const handleApprove = React.useCallback(() => {
    try {
      onApprove?.();
    } catch (e) {
      // Swallow to ensure popover closes immediately even if callback throws
    } finally {
      setOpen(false);
    }
  }, [onApprove, setOpen]);

  const handleCancel = React.useCallback(() => {
    onCancel?.();
    setOpen(false);
  }, [onCancel, setOpen]);

  return (
    <Popover open={actualOpen} onOpenChange={setOpen}>
      <PopoverTrigger asChild>{children}</PopoverTrigger>
      <PopoverContent
        align={align}
        side={side}
        sideOffset={sideOffset}
        onInteractOutside={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        className={cn(
          "pointer-events-auto space-y-6 w-[460px] py-5 before:border-t-4 before:border-b-2 before:border-foreground before:block before:h-2.5 before:mb-4",
          contentClassName,
        )}
      >
        <p className="mx-auto text-center text-sm leading-6 text-pretty text-foreground">
          {disclaimer}
        </p>
        <div className="mx-auto max-w-[250px] grid grid-cols-2 gap-2">
          <Button
            size="small"
            variant="default"
            onClick={handleCancel}
            className="w-full"
          >
            {cancelText}
          </Button>
          <Button
            size="small"
            variant="secondary"
            onClick={handleApprove}
            className="w-full"
          >
            {approveText}
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}

export default ApprovalPopover;

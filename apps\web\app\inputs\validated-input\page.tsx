"use client";

import { InputValidated } from "@workspace/ui/components/input-validated";

export default function Page() {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <section className="space-y-2">
        <h2 className="text-xl font-semibold">Default (no validation)</h2>
        <div className="max-w-xl">
          <InputValidated placeholder="Placeholder text" />
        </div>
      </section>

      <section className="space-y-2">
        <h2 className="text-xl font-semibold">Warning</h2>
        <div className="max-w-xl">
          <InputValidated
            placeholder="Placeholder text"
            status="warning"
            message="This looks a bit off. Please double-check."
          />
        </div>
      </section>

      <section className="space-y-2">
        <h2 className="text-xl font-semibold">Error</h2>
        <div className="max-w-xl">
          <InputValidated
            placeholder="Placeholder text"
            status="error"
            message="This field is required or invalid."
          />
        </div>
      </section>
    </div>
  );
}


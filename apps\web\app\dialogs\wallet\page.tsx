"use client";

import { useState } from "react";
import { WalletDialog, type TransactionData } from "@/components/wallet.dialog";
import { KYCDialog } from "@/components/kyc.dialog";
import { BankDepositDialog } from "@/components/bank-deposit.dialog";
import { IbanDialog } from "@/components/iban.dialog";
import { WithdrawalDialog } from "@/components/withdrawal.dialog";

// Mock bank data for deposit dialog
const mockBanks = [
  {
    name: "<PERSON><PERSON><PERSON> Kred<PERSON>",
    image: "/assets/wallet-lg.png", // Using wallet image as placeholder
    accountName: "ESENPAI YAZILIM SAN. VE TIC. LTD.",
    iban: "TR32 0010 0094 1000 6117 8688 16",
    description: "Yapı ve Kredi Bankası A.Ş.",
  },
];

// Mock data for SODA transactions
const mockSodaTransactions: TransactionData = {
  "2025-01": [
    {
      id: "TXN001",
      date: "2025-01-15",
      type: "Ya<PERSON><PERSON><PERSON>ım",
      description: "Banka Havalesi ile Para Yatırma",
      amount: 1000,
      newBalance: 5000,
    },
    {
      id: "TXN002",
      date: "2025-01-14",
      type: "Ödeme",
      description: "Aktivite Ödemesi - Mobile Legends Turnuvası",
      amount: -250,
      newBalance: 4000,
    },
    {
      id: "TXN003",
      date: "2025-01-12",
      type: "Talep",
      description: "Çekim Talebi Oluşturuldu",
      amount: -500,
      newBalance: 4250,
      status: "pending",
    },
    {
      id: "TXN005",
      date: "2025-01-08",
      type: "Hediye",
      description: "Arkadaştan SODA Hediyesi Alındı",
      amount: 100,
      newBalance: 4750,
    },
  ],
  "2025-02": [
    {
      id: "TXN004",
      date: "2025-02-10",
      type: "Kazanç",
      description: "Aktivite Tamamlama Ödülü",
      amount: 150,
      newBalance: 5150,
    },
    {
      id: "TXN006",
      date: "2025-02-05",
      type: "Çekim",
      description: "Para Çekme İşlemi Tamamlandı",
      amount: -300,
      newBalance: 5000,
    },
  ],
  "2025-03": [
    {
      id: "TXN007",
      date: "2025-03-01",
      type: "Gönderim",
      description: "Arkadaşa SODA Hediyesi Gönderildi",
      amount: -50,
      newBalance: 5100,
    },
  ],
};

// Mock data for CAP transactions
const mockCapsTransactions: TransactionData = {
  "2025-01": [
    {
      id: "CAP001",
      date: "2025-01-15",
      type: "Dönüşüm",
      description: "SODA'dan CAP'e Dönüştürme (11 TL → 1 CAP)",
      amount: 1,
      newBalance: 25,
    },
    {
      id: "CAP002",
      date: "2025-01-10",
      type: "Kazanç",
      description: "Günlük Görev Tamamlama Ödülü",
      amount: 5,
      newBalance: 24,
    },
    {
      id: "CAP004",
      date: "2025-01-05",
      type: "Hediye",
      description: "Arkadaştan CAP Hediyesi Alındı",
      amount: 2,
      newBalance: 19,
    },
  ],
  "2025-02": [
    {
      id: "CAP003",
      date: "2025-02-08",
      type: "Gönderim",
      description: "Arkadaşa CAP Hediyesi Gönderildi",
      amount: -3,
      newBalance: 19,
    },
  ],
  "2025-03": [
    {
      id: "CAP005",
      date: "2025-03-12",
      type: "Kazanç",
      description: "Haftalık Görev Tamamlama Bonusu",
      amount: 10,
      newBalance: 29,
    },
  ],
};

export default function WalletDialogPage() {
  const [walletOpen, setWalletOpen] = useState(true);
  const [kycOpen, setKycOpen] = useState(false);
  const [bankDepositOpen, setBankDepositOpen] = useState(false);
  const [ibanOpen, setIbanOpen] = useState(false);
  const [withdrawalOpen, setWithdrawalOpen] = useState(false);

  const handleDepositClick = () => {
    // First trigger KYC dialog, then bank deposit dialog
    setKycOpen(true);
  };

  const handleWithdrawClick = () => {
    // First trigger IBAN dialog, then withdrawal dialog
    setIbanOpen(true);
  };

  const handleKycSubmit = () => {
    setKycOpen(false);
    setBankDepositOpen(true);
  };

  const handleIbanSubmit = () => {
    setIbanOpen(false);
    setWithdrawalOpen(true);
  };

  const handleEarnClick = () => {
    // Disabled for now - under construction
    console.log("Earn feature under construction");
  };

  return (
    <WalletDialog
      open={walletOpen}
      onOpenChange={setWalletOpen}
      sodaBalance={5150}
      capsBalance={19}
      sodaTransactions={mockSodaTransactions}
      capsTransactions={mockCapsTransactions}
      onDepositClick={handleDepositClick}
      onWithdrawClick={handleWithdrawClick}
      onEarnClick={handleEarnClick}
    >
      <KYCDialog
        open={kycOpen}
        onOpenChange={setKycOpen}
        onSubmit={handleKycSubmit}
      >
        {bankDepositOpen && (
          <BankDepositDialog banks={mockBanks} descriptionCode="ES134KVK" />
        )}
      </KYCDialog>

      <IbanDialog
        open={ibanOpen}
        onOpenChange={setIbanOpen}
        onSubmit={handleIbanSubmit}
      >
        <WithdrawalDialog
          open={withdrawalOpen}
          onOpenChange={setWithdrawalOpen}
          balance={5150}
          min={20}
          max={5000}
          onSubmit={(data) => {
            console.log("Withdrawal submitted:", data);
            setWithdrawalOpen(false);
          }}
        />
      </IbanDialog>
    </WalletDialog>
  );
}

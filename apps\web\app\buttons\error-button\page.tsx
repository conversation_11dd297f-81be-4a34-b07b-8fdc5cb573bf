"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import { TooltipNotification } from "@workspace/ui/components/tooltip-notification";
import { AlertShape } from "@workspace/ui/components/shapes/alert-shape";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import { useState } from "react";

export default function TestErrorButtonPage() {
  const [showError, setShowError] = useState(false);

  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">Button Error State Test</h1>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Normal Button</h2>
        <Button>Normal Button</Button>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">But<PERSON> with Error</h2>
        <Button errorMessage="gönderilemedi">Button with Error</Button>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Toggle Error State</h2>
        <Button
          errorMessage={showError ? "gönderilemedi" : undefined}
          onClick={() => setShowError(!showError)}
        >
          {showError ? "Error State" : "Click to Show Error"}
        </Button>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Standalone Tooltip Test</h2>
        <TooltipNotification message="Test tooltip message">
          <AlertShape className="size-8">!</AlertShape>
        </TooltipNotification>
      </div>

      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Basic Radix Tooltip Test</h2>
        <Tooltip>
          <TooltipTrigger asChild>
            <button className="bg-blue-500 text-white px-4 py-2 rounded">
              Hover me for basic tooltip
            </button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Basic tooltip content</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  );
}

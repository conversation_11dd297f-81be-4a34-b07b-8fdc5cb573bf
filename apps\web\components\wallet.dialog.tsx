"use client";

import * as React from "react";
import { useState } from "react";
import Image from "next/image";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { Button } from "@workspace/ui/components/button";
import { Receipt, X } from "lucide-react";

// Transaction type definitions
export interface Transaction {
  id: string;
  date: string;
  type: string;
  description: string;
  amount: number;
  newBalance: number;
  status?: string;
}

export interface TransactionData {
  [month: string]: Transaction[];
}

export interface WalletDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  sodaBalance?: number;
  capsBalance?: number;
  sodaTransactions?: TransactionData;
  capsTransactions?: TransactionData;
  onDepositClick?: () => void;
  onWithdrawClick?: () => void;
  onEarnClick?: () => void;
  children?: React.ReactNode;
}

export function WalletDialog({
  open = false,
  onOpenChange,
  sodaBalance = 5150,
  capsBalance = 19,
  sodaTransactions = {},
  capsTransactions = {},
  onDepositClick,
  onWithdrawClick,
  onEarnClick,
  children,
}: WalletDialogProps) {
  const [activeTab, setActiveTab] = useState("soda");
  const [selectedMonth, setSelectedMonth] = useState("2025-01");

  // Generate months for current year
  const currentYear = new Date().getFullYear();
  const months = Array.from({ length: 12 }, (_, i) => {
    const month = i + 1;
    const monthStr = month.toString().padStart(2, "0");
    return {
      value: `${currentYear}-${monthStr}`,
      label: new Date(currentYear, i).toLocaleDateString("tr-TR", {
        month: "long",
        year: "numeric",
      }),
    };
  });

  const getCurrentTransactions = (): Transaction[] => {
    if (activeTab === "soda") {
      return sodaTransactions[selectedMonth] || [];
    } else {
      return capsTransactions[selectedMonth] || [];
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    const sign = amount >= 0 ? "+" : "";
    return `${sign}${amount} ${currency}`;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        onEscapeKeyDown={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        onInteractOutside={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        className="sm:max-w-[1000px] h-[600px] overflow-hidden w-full max-w-full flex flex-col pb-10"
      >
        <div className="w-full flex flex-col flex-1">
          <DialogHeader className="flex-row justify-between gap-4">
            <DialogTitle variant="stripe" className="w-2/3">
              {"CÜZDAN"}
            </DialogTitle>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mr-10 h-15 text-2xl font-mono font-bold">
                <TabsTrigger value="soda" className="w-50 relative first:pl-4">
                  <div className="flex items-center justify-between gap-3">
                    <div className="w-14 h-14 drop-shadow-md/40">
                      <Image
                        src="/assets/soda-lg.png"
                        alt="Soda"
                        fill
                        sizes="150px"
                        className="object-contain"
                      />
                    </div>
                    <div className="text-right">{sodaBalance}</div>
                  </div>
                </TabsTrigger>
                <TabsTrigger value="caps" className="w-50 relative pl-4">
                  <div className="flex items-center justify-between gap-4">
                    <div className="w-12 h-12 drop-shadow-md/40">
                      <Image
                        src="/assets/cap-lg.png"
                        alt="Cap"
                        fill
                        sizes="100px"
                        className="object-contain"
                      />
                    </div>
                    <div>{capsBalance}</div>
                  </div>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </DialogHeader>

          <div className="flex flex-col flex-1 space-y-6 mx-6 mt-6">
            {/* Month selector and action buttons */}
            <div className="flex justify-between items-center">
              <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Ay seçin" />
                </SelectTrigger>
                <SelectContent>
                  {months.map((month) => (
                    <SelectItem key={month.value} value={month.value}>
                      {month.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="flex gap-2">
                {activeTab === "soda" ? (
                  <>
                    <Button
                      onClick={onDepositClick}
                      size="small"
                      variant="default"
                    >
                      {"YATIR"}
                    </Button>
                    <Button
                      onClick={onWithdrawClick}
                      size="small"
                      variant="default"
                    >
                      {"ÇEK"}
                    </Button>
                  </>
                ) : (
                  <Button
                    onClick={onEarnClick}
                    disabled
                    size="small"
                    variant="default"
                    className="text-sm"
                  >
                    {"KAZAN"}
                  </Button>
                )}
              </div>
            </div>

            {/* Transactions table */}
            <div className="rounded-lg overflow-hidden w-full flex-1 flex flex-col min-h-0">
              <div className="flex-1 overflow-y-auto min-h-0">
                <Table className="w-full">
                  <TableHeader className="sticky top-0 bg-background">
                    <TableRow>
                      <TableHead className="w-24 min-w-[6rem] flex-shrink-0">
                        Tarih
                      </TableHead>
                      <TableHead className="w-20 min-w-[5rem] flex-shrink-0">
                        No
                      </TableHead>
                      <TableHead className="w-20 min-w-[5rem] flex-shrink-0">
                        Tür
                      </TableHead>
                      <TableHead className="flex-1 min-w-0">Açıklama</TableHead>
                      <TableHead className="w-24 min-w-[6rem] flex-shrink-0 text-right">
                        Miktar
                      </TableHead>
                      <TableHead className="w-24 min-w-[6rem] flex-shrink-0 text-right">
                        Bakiye
                      </TableHead>
                      <TableHead className="w-12 min-w-[3rem] flex-shrink-0"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getCurrentTransactions().length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={7}
                          className="text-center text-muted-foreground py-8"
                        >
                          Bu ay için işlem bulunamadı
                        </TableCell>
                      </TableRow>
                    ) : (
                      getCurrentTransactions().map((transaction) => (
                        <TableRow key={transaction.id}>
                          <TableCell className="w-24 min-w-[6rem] flex-shrink-0">
                            {new Date(transaction.date).toLocaleDateString(
                              "tr-TR",
                            )}
                          </TableCell>
                          <TableCell className="w-20 min-w-[5rem] flex-shrink-0 font-mono text-sm">
                            {transaction.id}
                          </TableCell>
                          <TableCell className="w-20 min-w-[5rem] flex-shrink-0">
                            {transaction.type}
                          </TableCell>
                          <TableCell className="flex-1 min-w-0 max-w-30">
                            <div
                              className="truncate"
                              title={transaction.description}
                            >
                              {transaction.description}
                            </div>
                          </TableCell>
                          <TableCell className="w-24 min-w-[6rem] flex-shrink-0 text-right">
                            <span
                              className={
                                transaction.amount >= 0
                                  ? "text-green-600"
                                  : "text-red-600"
                              }
                            >
                              {formatAmount(
                                transaction.amount,
                                activeTab === "soda" ? "SODA" : "CAP",
                              )}
                            </span>
                          </TableCell>
                          <TableCell className="w-24 min-w-[6rem] flex-shrink-0 text-right font-medium">
                            {transaction.newBalance}{" "}
                            {activeTab === "soda" ? "SODA" : "CAP"}
                          </TableCell>
                          <TableCell className="w-12 min-w-[3rem] flex-shrink-0">
                            {transaction.type === "Talep" &&
                            (transaction as any).status === "pending" ? (
                              <Button size="small" variant="destructive">
                                <X className="w-4 h-4" />
                              </Button>
                            ) : (
                              <Button size="small" variant="default">
                                <Receipt className="w-4 h-4" />
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
      {children}
    </Dialog>
  );
}

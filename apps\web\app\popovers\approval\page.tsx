"use client";

import * as React from "react";
import { ApprovalButton } from "@workspace/ui/components/approval.button";

export default function Page() {
  const [approvedCount, setApprovedCount] = React.useState(0);
  const [lastAction, setLastAction] = React.useState<string | null>(null);

  // Only control popover open state here; visuals are managed internally by the component
  const [open, setOpen] = React.useState(false);

  const handleOpenChange = (next: boolean) => {
    setOpen(next);
  };

  const handleApprove = async () => {
    // Simulate async work; popover will close immediately and the trigger will show processing
    await new Promise((r) => setTimeout(r, 4000));
    setApprovedCount((c) => c + 1);
    setLastAction("Onaylandı");
  };

  return (
    <div className="w-md mx-auto p-6 space-y-8">
      <section className="space-y-4">
        <div className="text-sm text-muted-foreground">
          <div>Onay sayı<PERSON>ı: {approvedCount}</div>
          <div>Son işlem: {lastAction ?? "-"}</div>
        </div>
        <div className="flex items-center gap-4">
          <ApprovalButton
            open={open}
            onOpenChange={handleOpenChange}
            disclaimer={
              <span>
                Bu işlem seçili operasyonu onaylayacak ve bir sonraki adıma
                geçecektir. Bu işlemi geri alamazsınız.
              </span>
            }
            approveText="Devam et"
            cancelText="İptal"
            onApprove={handleApprove}
            onCancel={() => setLastAction("İptal Edildi")}
            variant="primary"
          >
            {"ONAYLA"}
          </ApprovalButton>
        </div>
      </section>

      <section className="space-y-4">
        <h2 className="text-xl font-semibold">Başka bir örnek</h2>
        <ApprovalButton
          align="center"
          disclaimer="Bu gönderiyi yayınlamak istediğinizden emin misiniz?"
          approveText="Yayınla"
          cancelText="Geri"
          onApprove={() => setLastAction("Yayınlandı")}
          onCancel={() => setLastAction("Geri dönüldü")}
        >
          {"YAYINLA"}
        </ApprovalButton>
      </section>
    </div>
  );
}
